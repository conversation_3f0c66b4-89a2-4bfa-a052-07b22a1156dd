import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useExtensionIntegration } from '../hooks/use-extension-integration';
import { 
  Chrome, 
  Video, 
  Mouse, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  AlertCircle,
  Download,
  Play
} from 'lucide-react';

export function ExtensionIntegrationPanel() {
  const { state, actions } = useExtensionIntegration();

  if (!state.sessionId && !state.isLoading) {
    return null; // Don't show panel if no extension session
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusIcon = (hasData: boolean, isLoading: boolean) => {
    if (isLoading) return <Loader2 className="h-4 w-4 animate-spin" />;
    return hasData ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Chrome className="h-5 w-5" />
          <CardTitle className="text-lg">Chrome Extension</CardTitle>
          <Badge variant={state.isServerAvailable ? "default" : "destructive"}>
            {state.isServerAvailable ? "Connected" : "Offline"}
          </Badge>
        </div>
        <CardDescription>
          Screen recording imported from Chrome extension
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {state.error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700">{state.error}</span>
          </div>
        )}

        {state.isLoading ? (
          <div className="flex items-center justify-center py-6">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2 text-sm text-muted-foreground">Loading session data...</span>
          </div>
        ) : state.sessionData ? (
          <>
            {/* Session Info */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Session ID:</span>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  {state.sessionId?.slice(-8)}
                </code>
              </div>
              
              {state.sessionData.recordingMetadata && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Recording Type:</span>
                    <Badge variant="outline">
                      {state.sessionData.recordingMetadata.recordingType}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Recorded:</span>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(state.sessionData.recordingMetadata.timestamp)}
                    </span>
                  </div>

                  {state.sessionData.recordingMetadata.tabInfo && (
                    <div className="space-y-1">
                      <span className="text-sm font-medium">Source Tab:</span>
                      <div className="text-xs text-muted-foreground">
                        <div className="truncate">{state.sessionData.recordingMetadata.tabInfo.title}</div>
                        <div className="truncate opacity-70">{state.sessionData.recordingMetadata.tabInfo.url}</div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            <Separator />

            {/* Data Status */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  <span className="text-sm font-medium">Video Recording</span>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(state.hasVideo, false)}
                  <span className="text-xs text-muted-foreground">
                    {state.hasVideo ? 'Available' : 'Not found'}
                  </span>
                </div>
              </div>

              {state.sessionData.videoFile && (
                <div className="ml-6 text-xs text-muted-foreground">
                  <div>File: {state.sessionData.videoFile.originalName}</div>
                  <div>Uploaded: {formatTimestamp(state.sessionData.videoFile.uploadTime)}</div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mouse className="h-4 w-4" />
                  <span className="text-sm font-medium">Cursor Tracking</span>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(state.hasCursorData, false)}
                  <span className="text-xs text-muted-foreground">
                    {state.hasCursorData 
                      ? `${state.sessionData.cursorData?.length || 0} points`
                      : 'Not found'
                    }
                  </span>
                </div>
              </div>

              {state.hasCursorData && state.sessionData.cursorData && (
                <div className="ml-6 text-xs text-muted-foreground">
                  <div>
                    Clicks: {state.sessionData.cursorData.filter(p => p.action === 'click').length}
                  </div>
                  {state.sessionData.cursorUploadTime && (
                    <div>Uploaded: {formatTimestamp(state.sessionData.cursorUploadTime)}</div>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Actions */}
            <div className="space-y-2">
              <Button
                onClick={actions.addVideoToTimeline}
                disabled={!state.hasVideo}
                className="w-full"
                size="sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Add Video to Timeline
              </Button>

              {state.hasCursorData && (
                <Button
                  variant="outline"
                  onClick={() => {
                    // TODO: Implement cursor data preview/visualization
                    console.log('Cursor data:', actions.getCursorData());
                  }}
                  className="w-full"
                  size="sm"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Preview Cursor Data
                </Button>
              )}

              <Button
                variant="ghost"
                onClick={actions.clearSession}
                className="w-full"
                size="sm"
              >
                Clear Session
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-6 text-sm text-muted-foreground">
            No session data available
          </div>
        )}
      </CardContent>
    </Card>
  );
}
