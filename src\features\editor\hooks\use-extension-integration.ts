import { useState, useEffect, useCallback } from 'react';
import { extensionApi, ExtensionSessionData, ExtensionCursorData } from '../../../services/extension-api';
import { useLocalVideosStore } from '../store/use-local-videos-store';
import { dispatch } from '@designcombo/events';
import { ADD_VIDEO } from '@designcombo/state';
import { generateId } from '@designcombo/timeline';

export interface ExtensionIntegrationState {
  isLoading: boolean;
  sessionId: string | null;
  sessionData: ExtensionSessionData | null;
  error: string | null;
  hasVideo: boolean;
  hasCursorData: boolean;
  isServerAvailable: boolean;
}

export interface UseExtensionIntegrationReturn {
  state: ExtensionIntegrationState;
  actions: {
    loadSession: (sessionId: string) => Promise<boolean>;
    clearSession: () => void;
    addVideoToTimeline: () => Promise<boolean>;
    getCursorData: () => ExtensionCursorData[] | null;
    checkServerStatus: () => Promise<boolean>;
  };
}

export function useExtensionIntegration(): UseExtensionIntegrationReturn {
  const [state, setState] = useState<ExtensionIntegrationState>({
    isLoading: false,
    sessionId: null,
    sessionData: null,
    error: null,
    hasVideo: false,
    hasCursorData: false,
    isServerAvailable: false,
  });

  const { actions: videoActions } = useLocalVideosStore();

  // Check server status
  const checkServerStatus = useCallback(async (): Promise<boolean> => {
    try {
      const isAvailable = await extensionApi.isServerAvailable();
      setState(prev => ({ ...prev, isServerAvailable: isAvailable }));
      return isAvailable;
    } catch (error) {
      console.error('Error checking server status:', error);
      setState(prev => ({ ...prev, isServerAvailable: false }));
      return false;
    }
  }, []);

  // Load session data
  const loadSession = useCallback(async (sessionId: string): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const sessionResponse = await extensionApi.getSessionData(sessionId);
      
      if (!sessionResponse) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Session not found or server unavailable',
        }));
        return false;
      }

      const { data } = sessionResponse;
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        sessionId,
        sessionData: data,
        hasVideo: !!data.videoFile,
        hasCursorData: !!data.cursorData && data.cursorData.length > 0,
        error: null,
      }));

      // Update URL to include session ID
      extensionApi.constructor.updateUrlWithSession(sessionId);

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      return false;
    }
  }, []);

  // Clear session
  const clearSession = useCallback(() => {
    setState({
      isLoading: false,
      sessionId: null,
      sessionData: null,
      error: null,
      hasVideo: false,
      hasCursorData: false,
      isServerAvailable: false,
    });

    // Remove session from URL
    extensionApi.constructor.clearSessionFromUrl();
  }, []);

  // Add video to timeline
  const addVideoToTimeline = useCallback(async (): Promise<boolean> => {
    if (!state.sessionData?.videoFile) {
      return false;
    }

    try {
      const { videoFile } = state.sessionData;
      
      // Fetch the video file
      const response = await fetch(videoFile.url);
      if (!response.ok) {
        throw new Error('Failed to fetch video file');
      }

      const blob = await response.blob();
      const file = new File([blob], videoFile.originalName, { type: 'video/webm' });

      // Add to local videos store
      const localVideo = await videoActions.addVideo(file);

      // Add to timeline
      dispatch(ADD_VIDEO, {
        id: generateId(),
        resourceId: localVideo.id,
        name: localVideo.name,
        duration: localVideo.duration,
        width: localVideo.width,
        height: localVideo.height,
        aspectRatio: localVideo.aspectRatio,
        type: 'video',
        src: localVideo.objectUrl,
      });

      return true;
    } catch (error) {
      console.error('Error adding extension video to timeline:', error);
      setState(prev => ({
        ...prev,
        error: `Failed to add video: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }));
      return false;
    }
  }, [state.sessionData, videoActions]);

  // Get cursor data
  const getCursorData = useCallback((): ExtensionCursorData[] | null => {
    return state.sessionData?.cursorData || null;
  }, [state.sessionData]);

  // Auto-load session from URL on mount
  useEffect(() => {
    const sessionId = extensionApi.constructor.getSessionIdFromUrl();
    if (sessionId) {
      loadSession(sessionId);
    }
    
    // Check server status on mount
    checkServerStatus();
  }, [loadSession, checkServerStatus]);

  return {
    state,
    actions: {
      loadSession,
      clearSession,
      addVideoToTimeline,
      getCursorData,
      checkServerStatus,
    },
  };
}
