import express from 'express';
import cors from 'cors';
import { bundle } from '@remotion/bundler';
import { getCompositions, renderMedia } from '@remotion/renderer';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import multer from 'multer';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3001;

// Simple performance monitor utility
const performanceMonitor = {
  timers: {},
  startTimer(id, label) {
    this.timers[id] = { start: Date.now(), label };
  },
  endTimer(id) {
    const timer = this.timers[id];
    if (!timer) return null;
    const end = Date.now();
    const duration = end - timer.start;
    delete this.timers[id];
    return { label: timer.label, duration };
  }
};

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Store for tracking render jobs
const renderJobs = new Map();

// File path for persisting render jobs
const jobsFilePath = path.join(__dirname, 'render-jobs.json');

// Load existing render jobs from file on startup
async function loadRenderJobs() {
  try {
    const data = await fs.readFile(jobsFilePath, 'utf8');
    if (!data.trim()) {
      // File is empty, treat as no jobs
      console.warn('render-jobs.json is empty, initializing with no jobs.');
      return;
    }
    const jobs = JSON.parse(data);
    for (const [id, job] of Object.entries(jobs)) {
      // Convert date strings back to Date objects
      if (job.startTime) job.startTime = new Date(job.startTime);
      if (job.endTime) job.endTime = new Date(job.endTime);
      renderJobs.set(id, job);
    }
    console.log(`📂 Loaded ${renderJobs.size} render jobs from storage`);
  } catch (error) {
    if (error.code !== 'ENOENT') {
      console.error('Error loading render jobs:', error);
    }
  }
}

// Save render jobs to file
async function saveRenderJobs() {
  try {
    const jobs = Object.fromEntries(renderJobs);
    await fs.writeFile(jobsFilePath, JSON.stringify(jobs, null, 2));
  } catch (error) {
    console.error('Error saving render jobs:', error);
  }
}

// Enhanced renderJobs.set that also persists to file
function setRenderJob(id, job) {
  renderJobs.set(id, job);
  // Save to file asynchronously (don't wait for it)
  saveRenderJobs().catch(console.error);
}

// Load jobs on startup
loadRenderJobs();

// Store for uploaded files (blob URL replacements)
const uploadedFiles = new Map();

// Bundle cache to avoid re-bundling for every render
let bundleCache = {
  location: null,
  timestamp: null,
  entryPoint: null
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadsDir = path.join(__dirname, 'uploads');
    await fs.mkdir(uploadsDir, { recursive: true });
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

// Serve static files from renders and uploads directories
app.use('/renders', express.static(path.join(__dirname, 'renders')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Serve static files from the main application's public directory
// This allows Remotion to access preloaded images during rendering
app.use('/', express.static(path.join(__dirname, '../public')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Upload file endpoint (for converting blob URLs to accessible URLs)
app.post('/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: 'No file uploaded' });
    }

    const fileUrl = `${req.protocol}://${req.get('host')}/uploads/${req.file.filename}`;

    // Store the mapping for cleanup later
    uploadedFiles.set(req.file.filename, {
      originalName: req.file.originalname,
      path: req.file.path,
      uploadTime: new Date(),
    });

    res.json({
      success: true,
      fileUrl,
      filename: req.file.filename,
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Chrome Extension Integration Endpoints

// Store for extension session data
const extensionSessions = new Map();

// Upload video from Chrome extension
app.post('/extension/upload-video', upload.single('video'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: 'No video file uploaded' });
    }

    const sessionId = req.body.sessionId || `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const fileUrl = `${req.protocol}://${req.get('host')}/uploads/${req.file.filename}`;

    // Store session data
    const sessionData = extensionSessions.get(sessionId) || {};
    sessionData.videoFile = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      path: req.file.path,
      url: fileUrl,
      uploadTime: new Date(),
    };
    extensionSessions.set(sessionId, sessionData);

    // Also store in uploadedFiles for cleanup
    uploadedFiles.set(req.file.filename, {
      originalName: req.file.originalname,
      path: req.file.path,
      uploadTime: new Date(),
      sessionId: sessionId,
    });

    console.log(`Extension video uploaded: ${req.file.originalname} for session ${sessionId}`);

    res.json({
      success: true,
      sessionId,
      videoUrl: fileUrl,
      filename: req.file.filename,
    });
  } catch (error) {
    console.error('Extension video upload error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Upload cursor data from Chrome extension
app.post('/extension/upload-cursor-data', (req, res) => {
  try {
    const { sessionId, cursorData, recordingMetadata } = req.body;

    if (!sessionId) {
      return res.status(400).json({ success: false, error: 'Session ID is required' });
    }

    if (!cursorData || !Array.isArray(cursorData)) {
      return res.status(400).json({ success: false, error: 'Valid cursor data array is required' });
    }

    // Store cursor data in session
    const sessionData = extensionSessions.get(sessionId) || {};
    sessionData.cursorData = cursorData;
    sessionData.recordingMetadata = recordingMetadata || {};
    sessionData.cursorUploadTime = new Date();
    extensionSessions.set(sessionId, sessionData);

    console.log(`Extension cursor data uploaded: ${cursorData.length} points for session ${sessionId}`);

    res.json({
      success: true,
      sessionId,
      cursorPointsCount: cursorData.length,
      message: 'Cursor data uploaded successfully',
    });
  } catch (error) {
    console.error('Extension cursor data upload error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get session data for video editor
app.get('/extension/session/:sessionId', (req, res) => {
  try {
    const { sessionId } = req.params;
    const sessionData = extensionSessions.get(sessionId);

    if (!sessionData) {
      return res.status(404).json({ success: false, error: 'Session not found' });
    }

    res.json({
      success: true,
      sessionId,
      data: sessionData,
    });
  } catch (error) {
    console.error('Extension session retrieval error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Generate editor URL with session data
app.post('/extension/generate-editor-url', (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({ success: false, error: 'Session ID is required' });
    }

    const sessionData = extensionSessions.get(sessionId);
    if (!sessionData) {
      return res.status(404).json({ success: false, error: 'Session not found' });
    }

    // Generate editor URL with session parameters
    const editorBaseUrl = process.env.EDITOR_URL || 'http://localhost:5173';
    const editorUrl = `${editorBaseUrl}?extensionSession=${sessionId}`;

    res.json({
      success: true,
      editorUrl,
      sessionId,
      hasVideo: !!sessionData.videoFile,
      hasCursorData: !!sessionData.cursorData,
    });
  } catch (error) {
    console.error('Extension editor URL generation error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get available compositions
app.get('/compositions', async (req, res) => {
  try {
    console.log('Fetching compositions...');
    
    // Bundle the Remotion project
    const bundleLocation = await bundle({
      entryPoint: path.resolve(__dirname, '../src/index.ts'),
      webpackOverride: (config) => config,
    });

    // Get compositions
    const compositions = await getCompositions(bundleLocation);
    
    res.json({
      success: true,
      compositions: compositions.map(comp => ({
        id: comp.id,
        width: comp.width,
        height: comp.height,
        fps: comp.fps,
        durationInFrames: comp.durationInFrames,
      }))
    });
  } catch (error) {
    console.error('Error fetching compositions:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start a render job
app.post('/render', async (req, res) => {
  const renderId = `render_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  try {
    const {
      compositionId = 'Composition',
      inputProps = {},
      codec = 'h264',
      imageFormat = 'jpeg',
      quality = 80
    } = req.body;

    console.log(`Starting render ${renderId} for composition: ${compositionId}`);
    
    // Create renders directory if it doesn't exist
    const rendersDir = path.join(__dirname, 'renders');
    await fs.mkdir(rendersDir, { recursive: true });

    // Determine file extension based on codec
    const getFileExtension = (codec) => {
      switch (codec) {
        case 'vp8':
        case 'vp9':
          return 'webm';
        case 'h264':
        case 'h265':
        default:
          return 'mp4';
      }
    };

    const fileExtension = getFileExtension(codec);
    const outputLocation = path.join(rendersDir, `${renderId}.${fileExtension}`);
    
    // Store job info
    setRenderJob(renderId, {
      id: renderId,
      status: 'started',
      compositionId,
      inputProps,
      outputLocation,
      codec,
      fileExtension,
      startTime: new Date(),
      progress: 0
    });

    // Send immediate response with render ID
    res.json({
      success: true,
      renderId,
      status: 'started',
      message: 'Render job started'
    });

    // Start rendering asynchronously
    renderVideo(renderId, compositionId, inputProps, outputLocation, codec, imageFormat, quality);
    
  } catch (error) {
    console.error(`Error starting render ${renderId}:`, error);
    
    // Update job status
    if (renderJobs.has(renderId)) {
      setRenderJob(renderId, {
        ...renderJobs.get(renderId),
        status: 'error',
        error: error.message,
        endTime: new Date()
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message,
      renderId
    });
  }
});

// Get render status
app.get('/render/:renderId/status', (req, res) => {
  const { renderId } = req.params;
  const job = renderJobs.get(renderId);
  
  if (!job) {
    return res.status(404).json({
      success: false,
      error: 'Render job not found'
    });
  }
  
  const response = {
    success: true,
    renderId,
    status: job.status,
    progress: job.progress,
    startTime: job.startTime
  };
  
  if (job.status === 'completed') {
    response.downloadUrl = `/renders/${renderId}.mp4`;
    response.endTime = job.endTime;
    response.duration = job.endTime - job.startTime;
  }
  
  if (job.status === 'error') {
    response.error = job.error;
    response.endTime = job.endTime;
  }
  
  res.json(response);
});

// Download rendered video
app.get('/render/:renderId/download', async (req, res) => {
  const { renderId } = req.params;
  const job = renderJobs.get(renderId);
  
  if (!job) {
    return res.status(404).json({
      success: false,
      error: 'Render job not found'
    });
  }
  
  if (job.status !== 'completed') {
    return res.status(400).json({
      success: false,
      error: 'Render not completed yet'
    });
  }
  
  try {
    const filePath = job.outputLocation;
    const stat = await fs.stat(filePath);
    
    res.setHeader('Content-Type', 'video/mp4');
    res.setHeader('Content-Length', stat.size);
    res.setHeader('Content-Disposition', `attachment; filename="${renderId}.mp4"`);
    
    const fileStream = await fs.readFile(filePath);
    res.send(fileStream);
  } catch (error) {
    console.error(`Error downloading render ${renderId}:`, error);
    res.status(500).json({
      success: false,
      error: 'Error downloading file'
    });
  }
});

// Function to process input props and convert blob URLs to accessible URLs
async function processBlobUrls(inputProps) {
  const processedProps = JSON.parse(JSON.stringify(inputProps)); // Deep clone

  // Function to recursively find and replace blob URLs
  const replaceBlobUrls = async (obj) => {
    if (typeof obj === 'string' && obj.startsWith('blob:')) {
      console.log(`Warning: Found blob URL that cannot be accessed from server: ${obj}`);
      // For now, we'll return the blob URL and let the frontend handle the conversion
      return obj;
    }

    if (typeof obj === 'object' && obj !== null) {
      if (Array.isArray(obj)) {
        for (let i = 0; i < obj.length; i++) {
          obj[i] = await replaceBlobUrls(obj[i]);
        }
      } else {
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            obj[key] = await replaceBlobUrls(obj[key]);
          }
        }
      }
    }

    return obj;
  };

  return await replaceBlobUrls(processedProps);
}

// Async function to handle video rendering
async function renderVideo(renderId, compositionId, inputProps, outputLocation, codec, imageFormat, quality) {
  try {
    // Start performance monitoring
    performanceMonitor.startTimer(renderId, `Render ${renderId}`);
    performanceMonitor.startTimer(`${renderId}-bundle`, 'Bundle');

    console.log(`Bundling project for render ${renderId}...`);

    // Update status
    setRenderJob(renderId, {
      ...renderJobs.get(renderId),
      status: 'bundling',
      progress: 10
    });

    // Bundle the Remotion project
    const bundleLocation = await bundle({
      entryPoint: path.resolve(__dirname, '../src/index.ts'),
      webpackOverride: (config) => {
        // Configure webpack to use the render server as the public path
        config.output = config.output || {};
        config.output.publicPath = 'http://localhost:3001/';
        return config;
      },
    });

    console.log(`Getting compositions for render ${renderId}...`);
    
    // Update status
    setRenderJob(renderId, {
      ...renderJobs.get(renderId),
      status: 'preparing',
      progress: 20
    });

    // Get compositions
    const compositions = await getCompositions(bundleLocation, { inputProps });
    const composition = compositions.find((c) => c.id === compositionId);

    if (!composition) {
      throw new Error(`Composition '${compositionId}' not found`);
    }

    // Override composition dimensions with canvas size from inputProps
    const canvasWidth = inputProps.width || composition.width;
    const canvasHeight = inputProps.height || composition.height;
    const canvasFps = inputProps.fps || composition.fps;

    // Calculate duration in frames from duration in milliseconds
    const durationInSeconds = (inputProps.duration || 10000) / 1000; // Convert ms to seconds
    const canvasDurationInFrames = Math.ceil(durationInSeconds * canvasFps);

    // Debug logging for render server
    console.log('🖥️ RENDER SERVER DEBUG:');
    console.log(`📐 Original composition: ${composition.width}x${composition.height}`);
    console.log(`📦 Input props dimensions: ${inputProps.width}x${inputProps.height}`);
    console.log(`🎯 Final canvas dimensions: ${canvasWidth}x${canvasHeight}`);
    console.log(`🎬 FPS: ${canvasFps}, Duration: ${canvasDurationInFrames} frames`);
    console.log(`⚙️ Canvas settings:`, inputProps.canvasSettings);

    const finalComposition = {
      ...composition,
      width: canvasWidth,
      height: canvasHeight,
      fps: canvasFps,
      durationInFrames: canvasDurationInFrames,
    };

    // End compositions timing
    performanceMonitor.endTimer(`${renderId}-compositions`);
    performanceMonitor.startTimer(`${renderId}-render`, 'Video Render');

    console.log(`Starting video render for ${renderId}...`);
    console.log(`Canvas dimensions: ${canvasWidth}x${canvasHeight} @ ${canvasFps}fps, ${canvasDurationInFrames} frames`);

    // Update status
    setRenderJob(renderId, {
      ...renderJobs.get(renderId),
      status: 'rendering',
      progress: 30
    });

    // Render the video
    await renderMedia({
      composition: finalComposition,
      serveUrl: bundleLocation,
      codec,
      outputLocation,
      inputProps,
      imageFormat,
      jpegQuality: quality, // Updated parameter name
      onProgress: ({ progress }) => {
        const renderProgress = 30 + (progress * 0.7); // 30% to 100%
        setRenderJob(renderId, {
          ...renderJobs.get(renderId),
          progress: Math.round(renderProgress)
        });
      }
    });

    // End performance monitoring and get total metrics
    performanceMonitor.endTimer(`${renderId}-render`);
    const totalMetrics = performanceMonitor.endTimer(renderId);

    console.log(`Render ${renderId} completed successfully`);
    if (totalMetrics) {
      console.log(`📊 Total render time: ${totalMetrics.duration.toFixed(2)}ms (${(totalMetrics.duration / 1000).toFixed(2)}s)`);
    }

    // Update final status
    setRenderJob(renderId, {
      ...renderJobs.get(renderId),
      status: 'completed',
      progress: 100,
      endTime: new Date(),
      performanceMetrics: totalMetrics
    });

  } catch (error) {
    console.error(`Error rendering video ${renderId}:`, error);

    // End performance monitoring on error
    performanceMonitor.endTimer(`${renderId}-render`);
    const totalMetrics = performanceMonitor.endTimer(renderId);

    setRenderJob(renderId, {
      ...renderJobs.get(renderId),
      status: 'error',
      error: error.message,
      endTime: new Date(),
      performanceMetrics: totalMetrics
    });
  }
}

// Cleanup old uploaded files and extension sessions (run every hour)
setInterval(() => {
  const now = new Date();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours

  // Clean up uploaded files
  for (const [filename, fileInfo] of uploadedFiles.entries()) {
    if (now - fileInfo.uploadTime > maxAge) {
      try {
        fs.unlink(fileInfo.path, (err) => {
          if (err) {
            console.error(`Failed to delete old file ${filename}:`, err);
          } else {
            console.log(`Deleted old file: ${filename}`);
          }
        });
        uploadedFiles.delete(filename);
      } catch (error) {
        console.error(`Error cleaning up file ${filename}:`, error);
      }
    }
  }

  // Clean up old extension sessions
  for (const [sessionId, sessionData] of extensionSessions.entries()) {
    const sessionAge = sessionData.videoFile?.uploadTime || sessionData.cursorUploadTime;
    if (sessionAge && (now - sessionAge > maxAge)) {
      console.log(`Cleaning up old extension session: ${sessionId}`);
      extensionSessions.delete(sessionId);
    }
  }
}, 60 * 60 * 1000); // Run every hour

// Start server
app.listen(port, () => {
  console.log(`🎬 Remotion Render Server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🎥 Compositions: http://localhost:${port}/compositions`);
  console.log(`🔌 Extension integration: http://localhost:${port}/extension/*`);
});

export default app;
