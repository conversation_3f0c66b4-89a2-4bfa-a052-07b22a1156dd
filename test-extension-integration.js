/**
 * Test script for Chrome Extension Integration with React Video Editor
 * 
 * This script tests the complete workflow:
 * 1. Chrome extension records screen with cursor tracking
 * 2. Extension uploads video and cursor data to server
 * 3. Extension launches video editor with session data
 * 4. Video editor loads and displays content with cursor overlay
 */

// Test configuration
const CONFIG = {
  serverUrl: 'http://localhost:3001',
  editorUrl: 'http://localhost:5173',
  testVideoFile: null, // Will be created during test
  testCursorData: [
    { coords: { x: 100, y: 100 }, action: 'move', timestamp: Date.now() },
    { coords: { x: 150, y: 120 }, action: 'move', timestamp: Date.now() + 100 },
    { coords: { x: 200, y: 140 }, action: 'click', timestamp: Date.now() + 200 },
    { coords: { x: 250, y: 160 }, action: 'move', timestamp: Date.now() + 300 },
    { coords: { x: 300, y: 180 }, action: 'move', timestamp: Date.now() + 400 },
  ]
};

// Test utilities
class ExtensionIntegrationTest {
  constructor() {
    this.sessionId = null;
    this.results = {
      serverHealth: false,
      videoUpload: false,
      cursorUpload: false,
      sessionRetrieval: false,
      editorLaunch: false,
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Chrome Extension Integration Tests...\n');

    try {
      await this.testServerHealth();
      await this.testVideoUpload();
      await this.testCursorDataUpload();
      await this.testSessionRetrieval();
      await this.testEditorUrlGeneration();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.printResults();
    }
  }

  async testServerHealth() {
    console.log('1️⃣ Testing server health...');
    
    try {
      const response = await fetch(`${CONFIG.serverUrl}/health`);
      if (response.ok) {
        this.results.serverHealth = true;
        console.log('✅ Server is healthy\n');
      } else {
        throw new Error(`Server health check failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Server health check failed:', error.message);
      throw error;
    }
  }

  async testVideoUpload() {
    console.log('2️⃣ Testing video upload...');
    
    try {
      // Create a test video blob (simple canvas recording)
      const canvas = document.createElement('canvas');
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext('2d');
      
      // Draw a simple test pattern
      ctx.fillStyle = '#3b82f6';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#ffffff';
      ctx.font = '24px Arial';
      ctx.fillText('Test Video for Extension Integration', 50, 240);
      
      // Convert to blob
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'video/webm');
      });
      
      // Upload video
      const formData = new FormData();
      formData.append('video', blob, 'test-recording.webm');
      
      const response = await fetch(`${CONFIG.serverUrl}/extension/upload-video`, {
        method: 'POST',
        body: formData
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.sessionId = result.sessionId;
        this.results.videoUpload = true;
        console.log('✅ Video uploaded successfully');
        console.log(`   Session ID: ${this.sessionId}`);
        console.log(`   Video URL: ${result.videoUrl}\n`);
      } else {
        throw new Error(result.error || 'Video upload failed');
      }
    } catch (error) {
      console.error('❌ Video upload failed:', error.message);
      throw error;
    }
  }

  async testCursorDataUpload() {
    console.log('3️⃣ Testing cursor data upload...');
    
    try {
      const response = await fetch(`${CONFIG.serverUrl}/extension/upload-cursor-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: this.sessionId,
          cursorData: CONFIG.testCursorData,
          recordingMetadata: {
            recordingType: 'tab',
            timestamp: new Date().toISOString(),
            tabInfo: {
              title: 'Test Page',
              url: 'http://localhost/test'
            }
          }
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.results.cursorUpload = true;
        console.log('✅ Cursor data uploaded successfully');
        console.log(`   Points uploaded: ${result.cursorPointsCount}\n`);
      } else {
        throw new Error(result.error || 'Cursor data upload failed');
      }
    } catch (error) {
      console.error('❌ Cursor data upload failed:', error.message);
      throw error;
    }
  }

  async testSessionRetrieval() {
    console.log('4️⃣ Testing session data retrieval...');
    
    try {
      const response = await fetch(`${CONFIG.serverUrl}/extension/session/${this.sessionId}`);
      const result = await response.json();
      
      if (result.success) {
        this.results.sessionRetrieval = true;
        console.log('✅ Session data retrieved successfully');
        console.log('   Session data:', {
          hasVideo: !!result.data.videoFile,
          hasCursorData: !!result.data.cursorData,
          cursorPoints: result.data.cursorData?.length || 0,
          recordingType: result.data.recordingMetadata?.recordingType
        });
        console.log('');
      } else {
        throw new Error(result.error || 'Session retrieval failed');
      }
    } catch (error) {
      console.error('❌ Session retrieval failed:', error.message);
      throw error;
    }
  }

  async testEditorUrlGeneration() {
    console.log('5️⃣ Testing editor URL generation...');
    
    try {
      const response = await fetch(`${CONFIG.serverUrl}/extension/generate-editor-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: this.sessionId
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        this.results.editorLaunch = true;
        console.log('✅ Editor URL generated successfully');
        console.log(`   Editor URL: ${result.editorUrl}`);
        console.log(`   Has video: ${result.hasVideo}`);
        console.log(`   Has cursor data: ${result.hasCursorData}\n`);
        
        // Optionally open the editor (commented out for automated testing)
        // window.open(result.editorUrl, '_blank');
      } else {
        throw new Error(result.error || 'Editor URL generation failed');
      }
    } catch (error) {
      console.error('❌ Editor URL generation failed:', error.message);
      throw error;
    }
  }

  printResults() {
    console.log('📊 Test Results Summary:');
    console.log('========================');
    
    Object.entries(this.results).forEach(([test, passed]) => {
      const icon = passed ? '✅' : '❌';
      const status = passed ? 'PASSED' : 'FAILED';
      console.log(`${icon} ${test}: ${status}`);
    });
    
    const passedCount = Object.values(this.results).filter(Boolean).length;
    const totalCount = Object.keys(this.results).length;
    
    console.log(`\n📈 Overall: ${passedCount}/${totalCount} tests passed`);
    
    if (passedCount === totalCount) {
      console.log('🎉 All tests passed! Extension integration is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Check the logs above for details.');
    }
  }
}

// Run tests when script is loaded
if (typeof window !== 'undefined') {
  // Browser environment
  window.runExtensionIntegrationTest = () => {
    const test = new ExtensionIntegrationTest();
    test.runAllTests();
  };
  
  console.log('Extension Integration Test loaded.');
  console.log('Run window.runExtensionIntegrationTest() to start testing.');
} else {
  // Node.js environment
  const test = new ExtensionIntegrationTest();
  test.runAllTests();
}
