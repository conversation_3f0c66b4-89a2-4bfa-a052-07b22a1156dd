# Chrome Extension Integration with React Video Editor

This document describes the complete integration between the Chrome extension screen recorder and the React video editor, enabling seamless screen recording with cursor tracking and automatic video editing workflow.

## Overview

The integration provides a complete workflow:

1. **Chrome Extension**: Records screen with simultaneous cursor tracking
2. **Automatic Upload**: Uploads video and cursor data to the render server
3. **Editor Launch**: Automatically opens the React video editor with the recorded content
4. **Cursor Overlay**: Displays cursor movements and clicks as overlays in the video editor

## Architecture

```
Chrome Extension → Render Server → React Video Editor
     ↓                 ↓               ↓
Screen Recording   File Storage   Video Editing
Cursor Tracking    API Endpoints  Cursor Overlay
Auto Upload        Session Mgmt   Timeline Integration
```

## Components

### 1. Chrome Extension Enhancements

**Files Modified:**
- `control.js` - Added server integration and auto-launch functionality
- `control.html` - Added "Open Video Editor" button
- `background.js` - Existing cursor tracking functionality

**New Features:**
- Automatic video and cursor data upload after recording
- Session management with unique IDs
- Automatic video editor launch
- Manual editor re-opening capability

### 2. Server API Extensions

**File:** `remotion-render-server/server.js`

**New Endpoints:**
- `POST /extension/upload-video` - Upload recorded video files
- `POST /extension/upload-cursor-data` - Upload cursor tracking data
- `GET /extension/session/:sessionId` - Retrieve session data
- `POST /extension/generate-editor-url` - Generate editor URL with session

**Features:**
- Session-based data storage
- Automatic cleanup of old files
- CORS support for extension requests

### 3. React Video Editor Integration

**New Files:**
- `src/services/extension-api.ts` - Extension API client
- `src/features/editor/hooks/use-extension-integration.ts` - Integration hook
- `src/features/editor/components/extension-integration-panel.tsx` - UI panel
- `src/remotion/CursorOverlay.tsx` - Cursor overlay component

**Modified Files:**
- `src/features/editor/menu-item/local-media.tsx` - Added extension panel
- `src/remotion/VideoEditorComposition.tsx` - Added cursor overlay support
- `src/services/render-api.ts` - Added cursor data support

## Usage Workflow

### 1. Recording with Chrome Extension

1. Open the Chrome extension control panel
2. Select recording type (tab/window/screen)
3. Enable mouse tracking
4. Click "Start Recording"
5. Perform actions on screen
6. Click "Stop Recording"

### 2. Automatic Integration

After stopping recording:
1. Extension automatically uploads video file to server
2. Extension uploads cursor tracking data
3. Extension generates unique session ID
4. Extension opens video editor with session parameters
5. Video editor loads session data automatically

### 3. Video Editor Features

In the video editor:
- Extension integration panel shows session status
- "Add Video to Timeline" button loads the recorded video
- Cursor data is processed for overlay display
- Export includes cursor overlay in final video

## Configuration

### Server Configuration

The render server runs on `http://localhost:3001` by default. Update these URLs in:

**Chrome Extension (`control.js`):**
```javascript
this.serverUrl = 'http://localhost:3001';
this.editorUrl = 'http://localhost:5173';
```

**React App (`src/services/extension-api.ts`):**
```javascript
constructor(baseUrl: string = 'http://localhost:3001')
```

### Environment Variables

Set in your environment or `.env` file:
```bash
EDITOR_URL=http://localhost:5173
```

## API Reference

### Extension Upload Endpoints

#### Upload Video
```http
POST /extension/upload-video
Content-Type: multipart/form-data

video: File (WebM format)
sessionId: string (optional)
```

#### Upload Cursor Data
```http
POST /extension/upload-cursor-data
Content-Type: application/json

{
  "sessionId": "string",
  "cursorData": [
    {
      "coords": { "x": number, "y": number },
      "action": "move" | "click",
      "timestamp": number
    }
  ],
  "recordingMetadata": {
    "recordingType": "tab" | "window" | "screen",
    "timestamp": "ISO string",
    "tabInfo": {
      "title": "string",
      "url": "string"
    }
  }
}
```

#### Get Session Data
```http
GET /extension/session/:sessionId
```

#### Generate Editor URL
```http
POST /extension/generate-editor-url
Content-Type: application/json

{
  "sessionId": "string"
}
```

## Cursor Overlay Features

The cursor overlay component supports:

- **Real-time cursor position** based on video frame
- **Cursor trail** showing movement path
- **Click animations** with expanding circles
- **Customizable appearance** (colors, sizes, opacity)
- **Performance optimized** rendering

### Cursor Overlay Props

```typescript
interface CursorOverlayProps {
  cursorData: CursorOverlayData;
  showTrail?: boolean;        // Default: true
  trailLength?: number;       // Default: 30 frames
  cursorSize?: number;        // Default: 20px
  cursorColor?: string;       // Default: '#3b82f6'
  clickColor?: string;        // Default: '#ef4444'
  trailOpacity?: number;      // Default: 0.6
}
```

## Testing

### Manual Testing

1. Start the render server: `cd remotion-render-server && npm start`
2. Start the React app: `npm run dev`
3. Load the Chrome extension in developer mode
4. Open extension control panel and record screen
5. Verify automatic editor launch and data loading

### Automated Testing

Run the integration test:
```bash
node test-extension-integration.js
```

Or in browser console:
```javascript
// Load the test script first
window.runExtensionIntegrationTest();
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure server allows extension origin
2. **Upload Failures**: Check file size limits (500MB default)
3. **Session Not Found**: Verify session ID and server connectivity
4. **Editor Not Opening**: Check popup blockers and URL configuration

### Debug Mode

Enable debug logging in:
- Chrome extension: Check browser console
- Server: Monitor server logs
- React app: Check browser console and network tab

### File Cleanup

Old files are automatically cleaned up every hour. Manual cleanup:
```bash
# Remove old uploads
rm -rf remotion-render-server/uploads/*
# Remove old renders  
rm -rf remotion-render-server/renders/*
```

## Performance Considerations

- **File Size**: Large recordings may take time to upload
- **Cursor Data**: Extensive mouse movements create large datasets
- **Memory Usage**: Cursor overlay processing is optimized but monitor usage
- **Network**: Local server recommended for best performance

## Future Enhancements

Potential improvements:
- Real-time streaming instead of file upload
- Advanced cursor visualization options
- Multiple cursor tracking (multi-user)
- Integration with cloud storage
- Mobile device recording support
